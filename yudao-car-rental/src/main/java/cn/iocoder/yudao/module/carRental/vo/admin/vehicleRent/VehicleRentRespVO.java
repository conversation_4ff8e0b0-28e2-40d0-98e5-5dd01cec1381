package cn.iocoder.yudao.module.carRental.vo.admin.vehicleRent;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 租赁车辆 Response VO")
@Data
@ExcelIgnoreUnannotated
public class VehicleRentRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23346")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "车辆名称")
    @ExcelProperty("车辆名称")
    private String name;

    @Schema(description = "车辆编号（平台内部唯一标识）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("车辆编号（平台内部唯一标识）")
    private String vehicleNo;

    @Schema(description = "车牌号")
    @ExcelProperty("车牌号")
    private String licensePlate;

    @Schema(description = "车架号（VIN）")
    @ExcelProperty("车架号（VIN）")
    private String vin;

    @Schema(description = "所属门店ID（逻辑关联 rental_store.id）", requiredMode = Schema.RequiredMode.REQUIRED, example = "28651")
    @ExcelProperty("所属门店ID（逻辑关联 rental_store.id）")
    private Long storeId;

    @Schema(description = "品牌ID")
    private Long brandId;

    @Schema(description = "品牌")
    @ExcelProperty("品牌")
    private String brandName;

    @Schema(description = "型号")
    @ExcelProperty("型号")
    private String model;

    @Schema(description = "车辆详细说明（如颜色、配置等）")
    @ExcelProperty("车辆详细说明（如颜色、配置等）")
    private String detail;

    /**
     * 时租
     */
    @Schema(description = "时租价格（单位：元/小时）", example = "20184")
    private BigDecimal hourPrice;

    /**
     * 日租
     */
    @Schema(description = "日租价格（单位：元/天）", example = "20184")
    private BigDecimal dayPrice;

    /**
     * 月租
     */
    @Schema(description = "月租价格（单位：元/月）", example = "20184")
    private BigDecimal monthPrice;

    @Schema(description = "车辆图片，JSON数组格式（['url1', 'url2', ...]）")
    @ExcelProperty("车辆图片，JSON数组格式（['url1', 'url2', ...]）")
    private String images;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "状态（1=上架，0=下架，2=出售）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("状态（1=上架，0=下架，2=出售）")
    private Integer status;

    @Schema(description = "押金", example = "1000")
    @ExcelProperty("押金")
    private BigDecimal deposit;


    @Schema(description = "门店名称")
    private String storeName;


}