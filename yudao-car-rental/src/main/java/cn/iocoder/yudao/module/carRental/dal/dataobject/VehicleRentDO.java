package cn.iocoder.yudao.module.carRental.dal.dataobject;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * 租赁车辆 DO
 *
 * <AUTHOR>
 */
@TableName("rental_vehicle_rent")
@KeySequence("rental_vehicle_rent_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TenantIgnore
public class VehicleRentDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 车辆编号（平台内部唯一标识）
     */
    private String vehicleNo;
    /**
     * 车牌号
     */
    private String licensePlate;
    /**
     * 车架号（VIN）
     */
    private String vin;
    /**
     * 所属门店ID（逻辑关联 rental_store.id）
     */
    private Long storeId;
    /**
     * 品牌
     */
    private Long brandId;
    /**
     * 型号
     */
    private String model;
    /**
     * 车辆详细说明（如颜色、配置等）
     */
    private String detail;


    /**
     * 时租
     */
    private BigDecimal hourPrice;

    /**
     * 日租
     */
    private BigDecimal dayPrice;

    /**
     * 月租
     */
    private BigDecimal monthPrice;

    /**
     * 车辆图片，JSON数组格式（["url1", "url2", ...]）
     */
    private String images;

    /**
     * 状态（1=上架，0=下架，2=出售）
     */
    private Integer status;

    /**
     * 押金
     */
    private BigDecimal deposit;

    @TableField(exist = false)
    private String storeName;

    @TableField(exist = false)
    private String brandName;


}