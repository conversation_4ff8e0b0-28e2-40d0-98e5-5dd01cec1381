package cn.iocoder.yudao.module.carRental.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.carRental.dal.dataobject.*;
import cn.iocoder.yudao.module.carRental.dal.mysql.VehicleRentMapper;
import cn.iocoder.yudao.module.carRental.dto.AppVehicleSimpleDTO;
import cn.iocoder.yudao.module.carRental.enums.OrderTypeEnum;
import cn.iocoder.yudao.module.carRental.service.*;
import cn.iocoder.yudao.module.carRental.vo.admin.vehicleRent.VehicleRentPageReqVO;
import cn.iocoder.yudao.module.carRental.vo.admin.vehicleRent.VehicleRentSaveReqVO;
import cn.iocoder.yudao.module.carRental.vo.app.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.businessException;

/**
 * 租赁车辆 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class VehicleRentServiceImpl implements VehicleRentService {

    @Resource
    private VehicleRentMapper vehicleRentMapper;

    @Autowired
    @Lazy
    private StoreService storeService;

    @Autowired
    private MemberIdentityService memberIdentityService;

    @Autowired
    private UserCustomPriceService userCustomPriceService;

    @Autowired
    private BrandService brandService;

    @Autowired
    private MemberRentalService memberRentalService;

    @Override
    public Long createVehicleRent(VehicleRentSaveReqVO createReqVO) {
        // 插入
        VehicleRentDO vehicleRent = BeanUtils.toBean(createReqVO, VehicleRentDO.class);
        vehicleRentMapper.insert(vehicleRent);
        // 返回
        return vehicleRent.getId();
    }

    @Override
    public void updateVehicleRent(VehicleRentSaveReqVO updateReqVO) {
        // 校验存在
        validateVehicleRentExists(updateReqVO.getId());
        // 更新
        VehicleRentDO updateObj = BeanUtils.toBean(updateReqVO, VehicleRentDO.class);
        vehicleRentMapper.updateById(updateObj);
    }

    @Override
    public void deleteVehicleRent(Long id) {
        // 校验存在
        validateVehicleRentExists(id);
        // 删除
        vehicleRentMapper.deleteById(id);
    }

    @Override
        public void deleteVehicleRentListByIds(List<Long> ids) {
        // 校验存在
        validateVehicleRentExists(ids);
        // 删除
        vehicleRentMapper.deleteByIds(ids);
        }

    private void validateVehicleRentExists(List<Long> ids) {
        List<VehicleRentDO> list = vehicleRentMapper.selectByIds(ids);
        if (CollUtil.isEmpty(list) || list.size() != ids.size()) {
            throw businessException("租赁车辆不存在");
        }
    }

    private void validateVehicleRentExists(Long id) {
        if (vehicleRentMapper.selectById(id) == null) {
            throw businessException("租赁车辆不存在");
        }
    }

    @Override
    public VehicleRentDO getVehicleRent(Long id) {
        return vehicleRentMapper.selectById(id);
    }

    @Override
    public PageResult<VehicleRentDO> getVehicleRentPage(VehicleRentPageReqVO pageReqVO) {
        PageResult<VehicleRentDO> vehicleRentDOPageResult = vehicleRentMapper.selectPage(pageReqVO);
        if(!CollectionUtils.isEmpty(vehicleRentDOPageResult.getList())){
            vehicleRentDOPageResult.getList().forEach(vehicleRentDO -> {
                StoreDO store = storeService.getStore(vehicleRentDO.getStoreId());
                if(store != null){
                    vehicleRentDO.setStoreName(store.getName());
                }
                BrandDO brand = brandService.getBrand(vehicleRentDO.getBrandId());
                if(brand != null){
                    vehicleRentDO.setBrandName(brand.getName());
                }
            });
        }
        return vehicleRentDOPageResult;
    }

    @Override
    public PageResult<AppVehicleSimpleVO> findBugCarList(AppVehicleSimpleDTO dto) {
        // 构造查询条件
        LambdaQueryWrapper<VehicleRentDO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(VehicleRentDO::getStatus, true);

        if (dto.getBrandId() != null) {
            wrapper.eq(VehicleRentDO::getBrandId, dto.getBrandId());
        }

        // 初始排序，为了保证取最低价的时候有用（虽然后面会重排，但逻辑完整）
        wrapper.orderByAsc(VehicleRentDO::getHourPrice);

        // 查询所有符合条件记录
        List<VehicleRentDO> all = vehicleRentMapper.selectList(wrapper);

        // 分组：按 name 分组，保留每组中价格最低的那一条
        List<AppVehicleSimpleVO> distinctList = all.stream()
                .collect(Collectors.groupingBy(VehicleRentDO::getName))
                .values().stream()
                .map(list -> list.stream()
                        .min(Comparator.comparing(VehicleRentDO::getHourPrice))
                        .orElse(null))
                .filter(Objects::nonNull)
                .map(v -> {
                    AppVehicleSimpleVO vo = new AppVehicleSimpleVO();
                    vo.setId(v.getId());
                    vo.setName(v.getName());
                    vo.setImages(v.getImages());
                    vo.setPrice(v.getHourPrice());
                    return vo;
                })
                .collect(Collectors.toList());

        if ("asc".equalsIgnoreCase(dto.getPriceSort())) {
            distinctList.sort(Comparator.comparing(AppVehicleSimpleVO::getPrice));
        } else if ("desc".equalsIgnoreCase(dto.getPriceSort())) {
            distinctList.sort(Comparator.comparing(AppVehicleSimpleVO::getPrice).reversed());
        }

        // 分页处理
        int pageNo = dto.getPageNo();
        int pageSize = dto.getPageSize();
        long total = distinctList.size();

        List<AppVehicleSimpleVO> records = distinctList.stream()
                .skip((long)(pageNo - 1) * pageSize)
                .limit(pageSize)
                .collect(Collectors.toList());

        return new PageResult<>(records, total);
    }


    @Override
    public List<AppVehicleStoresSimpleVO> findRentalStoresList(Long id) {
        validateVehicleRentExists(id);
        VehicleRentDO vehicleRent = getVehicleRent(id);
        // 根据名称获取相同的
        LambdaQueryWrapper<VehicleRentDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VehicleRentDO::getName,vehicleRent.getName())
                .eq(VehicleRentDO::getStatus,true);
        List<VehicleRentDO> list = vehicleRentMapper.selectList(queryWrapper);

        List<AppVehicleStoresSimpleVO> collect = list.stream().map(rent -> {
            AppVehicleStoresSimpleVO vo = BeanUtils.toBean(rent, AppVehicleStoresSimpleVO.class);
            if (rent.getStoreId() != null) {
                // 获取门店信息
                StoreDO store = storeService.getStore(rent.getStoreId());
                if (store != null) {
                    AppStoresSimpleVO bean = BeanUtils.toBean(store, AppStoresSimpleVO.class);
                    // 获取门店图片
                    List<StoreImageDO> storeImages = storeService.getStoreImageListByStoreId(store.getId());
                    if(!CollectionUtils.isEmpty(storeImages)){
                        bean.setUrl(storeImages.get(0).getImageUrl());
                    }
                    vo.setStore(bean);
                }
            }
            return vo;
        }).collect(Collectors.toList());

        return collect;
    }

    @Override
    public BigDecimal findMinPriceVehicleByStoreId(Long storeId) {
        LambdaQueryWrapper<VehicleRentDO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(VehicleRentDO::getDeleted, 0)
                .eq(VehicleRentDO::getStatus, 1)
                .eq(VehicleRentDO::getStoreId, storeId)
                .orderByAsc(VehicleRentDO::getHourPrice)
                .last("LIMIT 1"); // 只查一条

        VehicleRentDO entity = vehicleRentMapper.selectOne(wrapper);
        if (entity == null) {
            return null;
        }
        return entity.getHourPrice();
    }

    @Override
    public List<Long> findBrandIdsListByStoreId(Long storeId) {
        // 根据门店ID获取品牌ID，去重
        LambdaQueryWrapper<VehicleRentDO> wrapper = Wrappers.lambdaQuery();
        wrapper.select(VehicleRentDO::getBrandId)
                .eq(VehicleRentDO::getStoreId, storeId)
                .eq(VehicleRentDO::getStatus, true);
        List<VehicleRentDO> vehicleSales = vehicleRentMapper.selectList(wrapper);
        if (vehicleSales == null || vehicleSales.isEmpty()) {
            return Collections.emptyList();
        }

        return vehicleSales.stream()
                .filter(v -> v != null && v.getBrandId() != null)
                .map(VehicleRentDO::getBrandId)
                .distinct()
                .collect(Collectors.toList());

    }

    @Override
    public Long countVehicleRentByStoreId(Long storeId) {
        LambdaQueryWrapper<VehicleRentDO> wrapper = Wrappers.lambdaQuery();
        wrapper.select(VehicleRentDO::getBrandId)
                .eq(VehicleRentDO::getStoreId, storeId)
                .eq(VehicleRentDO::getStatus, true);
        return vehicleRentMapper.selectCount(wrapper);
    }

    @Override
    public AppVehicleSaleOrderInitDetailRespVO initOrder(Long id) {
        // 判断车辆是否存在
        VehicleRentDO vehicleSale = getVehicleRent(id);
        if (vehicleSale == null) {
            throw businessException("车辆不存在");
        }

        AppVehicleSaleOrderInitDetailRespVO resultVo = new AppVehicleSaleOrderInitDetailRespVO();

        // 获取门店基本信息
        StoreDO store = storeService.getStore(vehicleSale.getStoreId());
        resultVo.setStore(BeanUtils.toBean(store, AppStoreBaseRespVO.class));

        // 获取用户基本信息
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        MemberDO member = memberRentalService.getMember(loginUserId);
        AppMemberInfoRespVO memberInfo = BeanUtils.toBean(member, AppMemberInfoRespVO.class);
        // 判断是否实名
        MemberIdentityDO identity = memberIdentityService.findMemberIdentitByUserId(loginUserId);
        if (identity != null) {
            memberInfo.setCertifiedStatus(identity.getStatus());
        } else {
            memberInfo.setCertifiedStatus(0);
        }
        resultVo.setMember(memberInfo);

        // 是否自定义租赁方案
        resultVo.setIsUserCustomPrice(userCustomPriceService.hasValidCurrentUserRentalAuth());

        AppVehicleBaseRespVO vehicleBase = BeanUtils.toBean(vehicleSale, AppVehicleBaseRespVO.class);
        vehicleBase.setType(OrderTypeEnum.RENT.getCode());
        // 需要设置租金
        vehicleBase.setDeposit(vehicleSale.getDeposit());
        // 获取品牌
        BrandDO brand = brandService.getBrand(vehicleSale.getBrandId());
        if(brand!= null){
            vehicleBase.setBrandName(brand.getName());
        }
        return resultVo;
    }

    @Override
    public void transfer(Long id, Long storeId) {
        VehicleRentDO vehicleRent = getVehicleRent(id);
        if (vehicleRent == null) {
            throw businessException("车辆不存在");
        }
        vehicleRent.setStoreId(storeId);
        vehicleRentMapper.updateById(vehicleRent);
    }

    @Override
    public void updateVehicleRentStatus(Long vehicleId, int status) {
        LambdaUpdateWrapper<VehicleRentDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(VehicleRentDO::getId, vehicleId)
                .set(VehicleRentDO::getStatus, status);
        vehicleRentMapper.update(updateWrapper);
    }

}