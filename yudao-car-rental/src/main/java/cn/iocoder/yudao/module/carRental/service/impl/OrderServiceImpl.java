package cn.iocoder.yudao.module.carRental.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.number.MoneyUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.carRental.config.AlipayMiniProgramProperties;
import cn.iocoder.yudao.module.carRental.dal.dataobject.*;
import cn.iocoder.yudao.module.carRental.dal.mysql.OrderMapper;
import cn.iocoder.yudao.module.carRental.dal.mysql.StoreBlacklistMapper;
import cn.iocoder.yudao.module.carRental.dto.*;
import cn.iocoder.yudao.module.carRental.enums.*;
import cn.iocoder.yudao.module.carRental.mq.producer.OrderProducer;
import cn.iocoder.yudao.module.carRental.service.*;
import cn.iocoder.yudao.module.carRental.utils.RentalCommonUtils;
import cn.iocoder.yudao.module.carRental.utils.RentalPriceCalculatorUtils;
import cn.iocoder.yudao.module.carRental.vo.admin.order.OrderPageReqVO;
import cn.iocoder.yudao.module.carRental.vo.admin.order.OrderRefundReqVO;
import cn.iocoder.yudao.module.carRental.vo.admin.order.OrderSaveReqVO;
import cn.iocoder.yudao.module.carRental.vo.aliPay.*;
import cn.iocoder.yudao.module.carRental.vo.app.AppOrderTradeCreateRespVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.businessException;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception0;

/**
 * <AUTHOR>
 * @Date 2025/7/23 16:40
 * @description 租车/购车统一订单 Service 实现类
 */
@Service
@Validated
@Slf4j
public class OrderServiceImpl implements OrderService {

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private StoreBlacklistMapper storeBlacklistMapper;

    @Resource
    private StoreService storeService;

    @Lazy
    @Resource
    private AlipayPayService alipayPayService;

    @Resource
    private VehicleSaleService vehicleSaleService;

    @Resource
    private VehicleRentService vehicleRentService;

    @Resource
    private MemberRentalService memberRentalService;

    @Resource
    private MemberIdentityService memberIdentityService;

    @Resource
    private StoreConfigService storeConfigService;

    @Resource
    private OrderProducer orderProducer;

    @Resource
    private OrderOperationLogService orderOperationLogService;

    @Resource
    private AlipayZmDepositPayService zmDepositPayService;

    @Resource
    private AlipayMiniProgramProperties properties;


    @Override
    public AppOrderTradeCreateRespVO createOrder(AppOrderTradeCreateDTO dto) {

        MemberDO member = memberRentalService.getMember(SecurityFrameworkUtils.getLoginUserId());
        if (member == null || member.getOpenid() == null) {
            throw businessException("用户信息不存在或未绑定小程序");
        }
        // 获取门店信息
        StoreDO store = storeService.getStore(dto.getStoreId());
        if(store == null){
            throw businessException("门店不存在");
        }

        // 下单校验用户是否合法
        this.orderLegalityVerification(member,store);
        // 判断订单类型
        if(OrderTypeEnum.RENT.getCode().equals(dto.getOrderType())){
            return createRentOrder(dto,member,store);
        } else if(OrderTypeEnum.BUY.getCode().equals(dto.getOrderType())){
            return createBuyOrder(dto,member,store);
        } else {
            throw businessException("非法类型的订单类型：" + dto.getOrderType());
        }
    }

    /**
     * @description:  租车订单创建
     * @Param: [dto, member, store]
     * @Author: OY
     * @Date:  2025/8/11 23:41
     * @return: cn.iocoder.yudao.module.carRental.vo.app.AppOrderTradeCreateRespVO
    **/
    private AppOrderTradeCreateRespVO createRentOrder(AppOrderTradeCreateDTO dto, MemberDO member, StoreDO store) {
        AppOrderTradeCreateRespVO resultVO = new AppOrderTradeCreateRespVO();

        String orderNo = RentalCommonUtils.generateOrderNumber("OR");
        OrderDO orderDO = new OrderDO();
        orderDO.setOrderNo(orderNo);
        orderDO.setUserId(member.getId());
        orderDO.setOrderType(OrderTypeEnum.RENT.getCode());
        orderDO.setVehicleId(dto.getVehicleId());

        orderDO.setStoreId(dto.getStoreId());
        orderDO.setStoreName(store.getName());

        // 校验租赁周期不能为空
        if(StrUtil.isEmpty(dto.getRentMode())){
            throw businessException("租赁周期不能为空");
        }
        if(dto.getRentStartTime() == null){
            throw businessException("租赁开始时间不能为空");
        }
        if(dto.getRentDuration() == null){
            throw businessException("租赁时长不能为空");
        }
        if(StrUtil.isEmpty(dto.getRentPaymentMode())){
            throw businessException("支付模式不能为空");
        }

        // 获取购车基本信息
        VehicleRentDO vehicleRent = vehicleRentService.getVehicleRent(dto.getVehicleId());
        if(vehicleRent == null){
            throw businessException("租车信息不存在");
        }
        orderDO.setVehicleName(vehicleRent.getName());
        orderDO.setVehicleVin(vehicleRent.getVin());
        orderDO.setLicensePlate(vehicleRent.getLicensePlate());
        orderDO.setVehicleImage(vehicleRent.getImages());

        // 租车
        orderDO.setRentMode(dto.getRentMode());
        orderDO.setRentStartTime(dto.getRentStartTime());
        orderDO.setRentDuration(dto.getRentDuration());
        orderDO.setRentPaymentMode(dto.getRentPaymentMode());
        orderDO.setDepositAmount(vehicleRent.getDeposit());
        if(RentalPeriodEnum.CUSTOM.getCode().equals(dto.getRentMode())){
            if(dto.getRentEndTime() == null){
                throw businessException("租赁结束时间不能为空");
            }
            orderDO.setRentEndTime(dto.getRentEndTime());
        }

        orderDO.setUserName(dto.getUserName());
        boolean validPhoneNumber = RentalCommonUtils.isValidPhoneNumber(dto.getUserPhone());
        if(!validPhoneNumber){
            throw businessException("手机号格式不正确");
        }
        orderDO.setUserPhone(dto.getUserPhone());

        RentalPeriodEnum rentalPeriodEnum = RentalPeriodEnum.fromCode(dto.getRentMode());
        if(rentalPeriodEnum == null){
            throw businessException("非法租赁周期");
        }

        // 金额不能为负数和0
        boolean validAmount = RentalCommonUtils.isValidAmount(dto.getAmount());
        if(!validAmount){
            throw businessException("金额非法");
        }

        // 金额计算
        if(!RentalPeriodEnum.CUSTOM.getCode().equals(dto.getRentMode())){
            // 计算金额，与前端传递过来金额对比
            BigDecimal calculatePrice = RentalPriceCalculatorUtils.calculateTotalPrice(rentalPeriodEnum, dto.getRentDuration(), vehicleRent);
            // 对比与前端传入金额与计算金额是否一致
            if(calculatePrice.compareTo(dto.getAmount()) > 0){
                throw businessException("订单金额异常");
            }
        }
        BigDecimal totalAmount = dto.getAmount().add(vehicleRent.getDeposit());

        // 取车方式
        orderDO.setPickupMethod(dto.getPickupMethod());
        if(PickupMethodEnum.DELIVER.getCode().equals(dto.getPickupMethod())){
            if(dto.getAddressId() == null){
                throw businessException("取车方式为送车上门时，地址不能为空");
            }
            orderDO.setAddressId(dto.getAddressId());
        }

        orderDO.setPayType(PayChannelEnum.ALIPAY.getCode());
        // 判断支付方式 先付后用、先用后付
        orderDO.setPickupMethod(dto.getPickupMethod());
        // 使用门店名称+车辆名称作为订单标题 租赁模式 * 租赁时长
        String subject = store.getName() + " - " + vehicleRent.getName() + " - "+RentalPeriodEnum.fromCode(dto.getRentMode()).getValue() + "*" + dto.getRentDuration();
        if(PaymentMethodEnum.PREPAY.getCode().equals(dto.getRentPaymentMode())){
            // 设置押金类型
            orderDO.setDepositType("normal");
            // 先付后用,调用支付宝
            AlipayTradeCreateDTO alipayTradeCreateDTO = new AlipayTradeCreateDTO();
            alipayTradeCreateDTO.setOutTradeNo(orderNo);
            alipayTradeCreateDTO.setSubject(subject);
            alipayTradeCreateDTO.setBuyerId(member.getOpenid());
            alipayTradeCreateDTO.setTotalAmount(totalAmount);
            AlipayTradeCreateRespVO respVO = alipayPayService.createTrade(alipayTradeCreateDTO);
            resultVO.setTradePayString(respVO.getTradePayString());
            resultVO.setTradeStatus(respVO.getTradeStatus());
            // 支付宝交易号
            resultVO.setTradeNo(respVO.getTradeNo());
            // 系统订单号
            resultVO.setOutTradeNo(respVO.getOutTradeNo());

            // 发送MQ队列，处理超时订单
            orderDO.setAlipayTradeNo(resultVO.getTradeNo());
            orderDO.setStatus(OrderStatusEnum.PENDING.getCode());
            orderDO.setAmount(dto.getAmount());
            int isInsertSuccess = orderMapper.insert(orderDO);

            if(isInsertSuccess > 0){
                // 修改租车车辆状态为已售
                vehicleRentService.updateVehicleRentStatus(dto.getVehicleId(), CarStatusEnum.SOLD.getCode());
            }
            // 发送订单超时消息到MQ
            orderProducer.sendOrderTimeoutMessage(orderDO);
            log.info("[createRentOrder][订单创建成功，已发送超时消息，订单号：{}]", orderDO.getOrderNo());

        } else if (PaymentMethodEnum.POSTPAY.getCode().equals(dto.getRentPaymentMode())){
            // 设置押金类型
            orderDO.setDepositType("alipay_free");
            // 先用后付(构建参数)
            AlipayZmDepositCreditOrderModel model = new AlipayZmDepositCreditOrderModel();
            model.setDepositProductMode("POSTPAY");
            model.setOrderTitle(subject);
            model.setOutOrderNo(orderNo);
            model.setOutRequestNo(orderNo);
            model.setProductCode("PRE_AUTH_ONLINE");
            model.setTotalAmount(totalAmount.toString());
            String creditServiceStr = String.format("{\"category\":\"%s\",\"serviceId\":\"%s\"}", properties.getZmCategory(), properties.getZmServiceId());
            model.setCreditServiceStr(creditServiceStr);
            AlipayZmDepositPostPaymentsModel paymentsModel = new AlipayZmDepositPostPaymentsModel();
            paymentsModel.setProductModeAmount(totalAmount.toString());
            paymentsModel.setProductModeName(subject);
            model.setPostPaymentsModel(paymentsModel);
            String depositOrder = zmDepositPayService.createDepositOrder(model);
            resultVO.setTradePayString(depositOrder);
            resultVO.setTradeStatus("WAIT_BUYER_PAY");
            // 支付宝交易号
            resultVO.setTradeNo(orderNo);
            // 系统订单号
            resultVO.setOutTradeNo(orderNo);

            // 处理超时订单
            orderDO.setAlipayTradeNo("");
            orderDO.setStatus(OrderStatusEnum.PENDING.getCode());
            orderDO.setAmount(dto.getAmount());
            orderDO.setQuantity(dto.getQuantity());
            int isInsertSuccess = orderMapper.insert(orderDO);
            if(isInsertSuccess > 0){
                // 修改车辆状态为已售
                vehicleRentService.updateVehicleRentStatus(dto.getVehicleId(), CarStatusEnum.SOLD.getCode());
            }
            // 发送订单芝麻免押超时消息到MQ
            orderProducer.sendOrderTimeoutMessage(orderDO);
            log.info("[createRentOrder][订单创建成功，已发送芝麻免押超时消息，订单号：{}]", orderDO.getOrderNo());


        } else {
            throw businessException("非法类型的支付方式：" + dto.getRentPaymentMode());
        }
        // 记录订单创建操作日志
        orderOperationLogService.createOrderOperationLog(
                orderDO,
                OrderOperationTypeEnum.CREATE.getCode(),
                SecurityFrameworkUtils.getLoginUserId(),
                "user",
                null,
                "创建租车订单"
        );

        return resultVO;
    }

    /**
     * @description: 购车订单创建
     * @Param: [dto]
     * @Author: OY
     * @Date:  2025/7/23 17:06
     * @return: cn.iocoder.yudao.module.carRental.vo.app.AppOrderTradeCreateRespVO
     **/
    @Transactional
    public AppOrderTradeCreateRespVO createBuyOrder(AppOrderTradeCreateDTO dto,MemberDO member,StoreDO store) {
        AppOrderTradeCreateRespVO resultVO = new AppOrderTradeCreateRespVO();


        String orderNo = RentalCommonUtils.generateOrderNumber("OB");

        OrderDO orderDO = new OrderDO();
        orderDO.setOrderNo(orderNo);
        orderDO.setUserId(member.getId());
        orderDO.setOrderType(OrderTypeEnum.BUY.getCode());
        orderDO.setVehicleId(dto.getVehicleId());
        orderDO.setRentMode(dto.getRentMode());
        orderDO.setRentStartTime(dto.getRentStartTime());
        orderDO.setRentDuration(dto.getRentDuration());
        orderDO.setRentPaymentMode(dto.getRentPaymentMode());


        orderDO.setStoreId(dto.getStoreId());
        orderDO.setStoreName(store.getName());

        // 获取购车基本信息
        VehicleSaleDO vehicleSale = vehicleSaleService.getVehicleSale(dto.getVehicleId());
        if(vehicleSale == null){
            throw businessException("购车信息不存在");
        }
        orderDO.setVehicleName(vehicleSale.getName());
        orderDO.setVehicleVin(vehicleSale.getVin());
        orderDO.setLicensePlate(vehicleSale.getLicensePlate());
        orderDO.setVehicleImage(vehicleSale.getImages());

        orderDO.setUserName(dto.getUserName());
        boolean validPhoneNumber = RentalCommonUtils.isValidPhoneNumber(dto.getUserPhone());
        if(!validPhoneNumber){
            throw businessException("手机号格式不正确");
        }
        orderDO.setUserPhone(dto.getUserPhone());

        // 金额不能为负数和0
        boolean validAmount = RentalCommonUtils.isValidAmount(dto.getAmount());
        if(!validAmount){
            throw businessException("金额非法");
        }

        // 价格 * 数量
        BigDecimal totalAmount = MoneyUtils.priceMultiply(vehicleSale.getPrice(), BigDecimal.valueOf(dto.getQuantity()));
        // 判断金额是否与购车金额一致
        if(!dto.getAmount().equals(totalAmount)){
            throw businessException("订单金额异常");
        }


        // 取车方式
        orderDO.setPickupMethod(dto.getPickupMethod());
        if(PickupMethodEnum.DELIVER.getCode().equals(dto.getPickupMethod())){
            if(dto.getAddressId() == null){
                throw businessException("取车方式为送车上门时，地址不能为空");
            }
            orderDO.setAddressId(dto.getAddressId());
        }

        orderDO.setPayType(PayChannelEnum.ALIPAY.getCode());
        // 判断支付方式 先付后用、先用后付
        orderDO.setPickupMethod(dto.getPickupMethod());

        if(PaymentMethodEnum.PREPAY.getCode().equals(dto.getRentPaymentMode())){
            // 暂时只支持全款
            orderDO.setPurchasePlan(dto.getPurchasePlan());

            // 先付后用,调用支付宝
            AlipayTradeCreateDTO alipayTradeCreateDTO = new AlipayTradeCreateDTO();
            alipayTradeCreateDTO.setOutTradeNo(orderNo);
            // 使用门店名称+车辆名称作为订单标题*数量
            String subject = store.getName() + " - " + vehicleSale.getName() + " * " + dto.getQuantity();
            alipayTradeCreateDTO.setSubject(subject);
            alipayTradeCreateDTO.setBuyerId(member.getOpenid());
            alipayTradeCreateDTO.setTotalAmount(dto.getAmount());
            AlipayTradeCreateRespVO respVO = alipayPayService.createTrade(alipayTradeCreateDTO);
            resultVO.setTradePayString(respVO.getTradePayString());
            resultVO.setTradeStatus(respVO.getTradeStatus());
            // 支付宝交易号
            resultVO.setTradeNo(respVO.getTradeNo());
            // 系统订单号
            resultVO.setOutTradeNo(respVO.getOutTradeNo());

            // 发送MQ队列，处理超时订单
            orderDO.setAlipayTradeNo(resultVO.getTradeNo());
            orderDO.setStatus(OrderStatusEnum.PENDING.getCode());
            orderDO.setAmount(dto.getAmount());
            orderDO.setQuantity(dto.getQuantity());
            int isInsertSuccess = orderMapper.insert(orderDO);

            if(isInsertSuccess > 0){
                // 修改车辆状态为已售
                vehicleSaleService.updateVehicleSaleStatus(dto.getVehicleId(), CarStatusEnum.SOLD.getCode());
            }
            // 发送订单超时消息到MQ
            orderProducer.sendOrderTimeoutMessage(orderDO);
            log.info("[createBuyOrder][订单创建成功，已发送超时消息，订单号：{}]", orderDO.getOrderNo());
        } else if (PaymentMethodEnum.POSTPAY.getCode().equals(dto.getRentPaymentMode())){
            throw businessException("暂不支持先用后付的支付方式");
        } else {
            throw businessException("非法类型的支付方式：" + dto.getRentPaymentMode());
        }
        // 记录订单创建操作日志
        orderOperationLogService.createOrderOperationLog(
                orderDO,
                OrderOperationTypeEnum.CREATE.getCode(),
                SecurityFrameworkUtils.getLoginUserId(),
                "user",
                null,
                "创建购车订单"
        );
        return resultVO;
    }

    private void orderLegalityVerification(MemberDO member,StoreDO store) {
        // 获取实名认证信息
        MemberIdentityDO identity = memberIdentityService.findMemberIdentitByUserId(member.getId());
        // 判断是否实名
        if (identity == null || identity.getStatus() != 1) {
            throw exception0(507,"未实名认证");
        }
        StoreConfigDO storeConfig = storeConfigService.getStoreConfigByStoreId(store.getId());
        if(storeConfig == null){
            return;
        }

        // 判断用户年龄是否在区间中
        if(storeConfig.getMinAge() != null && storeConfig.getMaxAge() != null){
            // 从身份证中获取年龄
            int age = RentalCommonUtils.getAgeFromIdCard(identity.getIdCardNumber());
            if(age < storeConfig.getMinAge() || age > storeConfig.getMaxAge()){
                throw exception0(507,"用户年龄不在允许范围内");
            }
        }

        // 判断禁止本门店在租用户
        if(storeConfig.getBanSelfRent() != null && storeConfig.getBanSelfRent()){
            // 判断用户是否在本门店在租
            LambdaQueryWrapper<OrderDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OrderDO::getUserId, member.getId())
                    .eq(OrderDO::getOrderType, OrderTypeEnum.RENT.getCode())
                    .eq(OrderDO::getStatus, OrderStatusEnum.PAID.getCode())
                    .eq(OrderDO::getStoreId, store.getId());
            if(orderMapper.selectCount(queryWrapper) > 0){
                throw businessException("用户在本门店有在租订单");
            }
        }

        // 禁止其他门店在租用户
        if(storeConfig.getBanOtherRent() != null && storeConfig.getBanOtherRent()){
            LambdaQueryWrapper<OrderDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OrderDO::getUserId, member.getId())
                    .eq(OrderDO::getOrderType, OrderTypeEnum.RENT.getCode())
                    .eq(OrderDO::getStatus, OrderStatusEnum.PAID.getCode())
                    .ne(OrderDO::getStoreId, store.getId());
            if(orderMapper.selectCount(queryWrapper) > 0){
                throw businessException("用户在其他门店有在租订单");
            }
        }

        // 禁止本店和其他门店黑名单用户
        if(storeConfig.getBanOtherBlack() != null && storeConfig.getBanOtherBlack()){
            LambdaQueryWrapper<StoreBlacklistDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StoreBlacklistDO::getUserId, member.getId())
                    .eq(StoreBlacklistDO::getStoreId, store.getId());
            if(storeBlacklistMapper.selectCount(queryWrapper) > 0){
                throw businessException("用户在黑名单中");
            }
        }
    }


    private void validateOrderExists(List<Long> ids) {
        List<OrderDO> list = orderMapper.selectByIds(ids);
        if (CollUtil.isEmpty(list) || list.size() != ids.size()) {
            throw businessException("订单不存在");
        }
    }

    private void validateOrderExists(Long id) {
        if (orderMapper.selectById(id) == null) {
            throw businessException("订单不存在");
        }
    }

    @Override
    public OrderDO getOrder(Long id) {
        return orderMapper.selectById(id);
    }

    @Override
    public Boolean cancelOrder(AppOrderDTO dto) {
        OrderDO order = getOrder(dto.getId());
        if (order == null) {
            throw businessException("订单不存在");
        }

        // 记录取消前的状态
        String previousStatus = order.getStatus();

        boolean isRentOrder = OrderTypeEnum.RENT.getCode().equals(order.getOrderType());
        boolean isBuyOrder = OrderTypeEnum.BUY.getCode().equals(order.getOrderType());
        
        // 根据订单类型检查状态是否允许取消
        if (isRentOrder) {
            // 租车订单(OR)，允许PENDING状态取消
            if (!OrderStatusEnum.PENDING.getCode().equals(order.getStatus())) {
                log.info("[cancelOrder][租车订单状态不允许取消，订单号：{}，当前状态：{}]", 
                        order.getOrderNo(), order.getStatus());
                throw businessException("租车订单状态不可取消");
            }
        } else if (isBuyOrder) {
            // 购车订单(OB)，只允许PENDING状态取消
            if (!OrderStatusEnum.PENDING.getCode().equals(order.getStatus())) {
                log.info("[cancelOrder][购车订单状态不允许取消，订单号：{}，当前状态：{}]", 
                        order.getOrderNo(), order.getStatus());
                throw businessException("购车订单状态不可取消");
            }
        } else {
            // 未知订单类型
            log.warn("[cancelOrder][未知订单类型，不允许取消，订单号：{}]", order.getOrderNo());
            throw businessException("未知订单类型");
        }

        // 更新订单状态
        order.setStatus(OrderStatusEnum.CANCELLED.getCode());
        order.setCancelTime(LocalDateTime.now());
        orderMapper.updateById(order);

        // 记录订单取消操作日志
        String orderTypeDesc = isRentOrder ? "租车" : (isBuyOrder ? "购车" : "未知类型");
        orderOperationLogService.createOrderOperationLog(
                order,
                OrderOperationTypeEnum.CANCEL.getCode(),
                SecurityFrameworkUtils.getLoginUserId(),
                "user",
                previousStatus,
                "用户取消" + orderTypeDesc + "订单"
        );

        return true;
    }

    @Override
    public PageResult<OrderDO> getOrderPage(OrderPageReqVO pageReqVO) {
        return orderMapper.selectPage(pageReqVO);
    }

    @Override
    public void updateOrderStatus(String outTradeNo, String tradeNo, String status) {
        OrderDO order = getOrderByOrderNo(outTradeNo);
        if (order == null) {
            return;
        }

        // 记录更新前的状态
        String previousStatus = order.getStatus();

        // 记录订单状态更新操作日志
        String operationType;
        String remark;
        if (OrderStatusEnum.PAID.getCode().equals(status)) {
            operationType = OrderOperationTypeEnum.PAY.getCode();
            order.setPayTime(LocalDateTime.now());
            remark = "订单支付成功";
        } else if (OrderStatusEnum.CANCELLED.getCode().equals(status)) {
            operationType = OrderOperationTypeEnum.SYSTEM_CANCEL.getCode();
            order.setCancelTime(LocalDateTime.now());
            remark = "系统取消订单";
        } else if(OrderStatusEnum.IN_PROGRESS.getCode().equals(status)){
            operationType = OrderOperationTypeEnum.PAY.getCode();
            order.setPayTime(LocalDateTime.now());
            remark = "订单支付成功";
        }else {
            operationType = OrderOperationTypeEnum.MODIFY.getCode();
            remark = "更新订单状态";
        }

        // 更新订单状态
        order.setStatus(status);
        order.setAlipayTradeNo(tradeNo);
        orderMapper.updateById(order);

        orderOperationLogService.createOrderOperationLog(
                order,
                operationType,
                0L, // 系统操作
                "system",
                previousStatus,
                remark
        );
    }

    @Override
    public OrderDO getOrderByOrderNo(String orderNo) {
        LambdaQueryWrapper<OrderDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderDO::getOrderNo, orderNo);
        return orderMapper.selectOne(queryWrapper);
    }

    @Override
    public void handleOrderTimeout(OrderDO order) {
        if (order == null) {
            return;
        }

        String previousStatus = order.getStatus();

        // 根据订单类型检查相应的状态
        boolean isRentOrder = OrderTypeEnum.RENT.getCode().equals(order.getOrderType());
        boolean isBuyOrder = OrderTypeEnum.BUY.getCode().equals(order.getOrderType());
        
        if (isRentOrder && !OrderStatusEnum.PENDING.getCode().equals(previousStatus)) {
            log.info("[handleOrderTimeout][租车订单状态不是进行中，不处理，订单号：{}，当前状态：{}]", 
                    order.getOrderNo(), previousStatus);
            return;
        } else if (isBuyOrder && !OrderStatusEnum.PENDING.getCode().equals(previousStatus)) {
            log.info("[handleOrderTimeout][购车订单状态不是待支付，不处理，订单号：{}，当前状态：{}]", 
                    order.getOrderNo(), previousStatus);
            return;
        } else if (!isRentOrder && !isBuyOrder) {
            log.warn("[handleOrderTimeout][未知订单类型，不处理，订单号：{}]", order.getOrderNo());
            return;
        }

        // 根据订单类型和押金类型选择不同的查询方式
        if (isRentOrder && PaymentMethodEnum.POSTPAY.getCode().equals(order.getRentPaymentMode())) {
            // 芝麻免押订单，使用芝麻信用查询接口
            log.info("[handleOrderTimeout][租车订单为芝麻免押类型，使用芝麻信用查询接口，订单号：{}]", order.getOrderNo());
            AlipayZmDepositQueryRespVO zmRespVO;
            try {
                // 查询芝麻免押订单状态，operationType为null表示查询授权单状态
                zmRespVO = zmDepositPayService.queryOrder(order.getOrderNo(), order.getOrderNo(), null);
            } catch (Exception e) {
                // 查询异常直接取消订单
                log.error("[handleOrderTimeout][查询芝麻免押订单异常，订单号：{}]", order.getOrderNo(), e);
                zmRespVO = null;
            }

            // 如果查询成功且状态为AUTHORIZED，则更新为已支付
            if (zmRespVO != null && "AUTHORIZED".equals(zmRespVO.getOrderStatus())) {
                updateOrderStatus(order.getOrderNo(), order.getAlipayTradeNo(), OrderStatusEnum.PAID.getCode());
                log.info("[handleOrderTimeout][芝麻免押订单已授权，更新为已支付，订单号：{}]", order.getOrderNo());
                return;
            }
            
            // 如果状态为CLOSED，则取消订单
            if (zmRespVO != null && "CLOSED".equals(zmRespVO.getOrderStatus())) {
                log.info("[handleOrderTimeout][芝麻免押订单已关闭，取消订单，订单号：{}]", order.getOrderNo());
                // 执行取消订单逻辑
                order.setStatus(OrderStatusEnum.CANCELLED.getCode());
                order.setCancelTime(LocalDateTime.now());
                orderMapper.updateById(order);
                
                // 记录订单超时操作日志
                orderOperationLogService.createOrderOperationLog(
                        order,
                        OrderOperationTypeEnum.TIMEOUT.getCode(),
                        0L,
                        "system",
                        previousStatus,
                        "芝麻免押订单超时自动取消"
                );
                return;
            }
            
            // 其他状态（如INIT）或查询失败，也取消订单
            log.info("[handleOrderTimeout][芝麻免押订单状态异常或查询失败，取消订单，订单号：{}]", order.getOrderNo());
            order.setStatus(OrderStatusEnum.CANCELLED.getCode());
            order.setCancelTime(LocalDateTime.now());
            orderMapper.updateById(order);
            
            // 记录订单超时操作日志
            orderOperationLogService.createOrderOperationLog(
                    order,
                    OrderOperationTypeEnum.TIMEOUT.getCode(),
                    0L,
                    "system",
                    previousStatus,
                    "芝麻免押订单超时自动取消"
            );
        } else {
            // 普通支付宝订单，使用支付宝查询接口
            log.info("[handleOrderTimeout][使用支付宝查询接口，订单号：{}]", order.getOrderNo());
            // 查询支付宝订单状态
            AlipayTradeQueryDTO queryDTO = new AlipayTradeQueryDTO();
            queryDTO.setOutTradeNo(order.getOrderNo());

            AlipayTradeQueryRespVO respVO;
            try {
                respVO = alipayPayService.queryTrade(queryDTO);
            } catch (Exception e) {
                // 查询异常直接取消订单
                log.error("[handleOrderTimeout][查询支付宝订单异常，订单号：{}]", order.getOrderNo(), e);
                respVO = null;
            }

            // 如果已支付则更新为已支付
            if (respVO != null && "TRADE_SUCCESS".equals(respVO.getTradeStatus())) {
                updateOrderStatus(order.getOrderNo(), respVO.getTradeNo(), OrderStatusEnum.PAID.getCode());
                log.info("[handleOrderTimeout][支付宝订单已支付，更新为已支付，订单号：{}]", order.getOrderNo());
                return;
            }
            
            // 否则更新订单状态为已取消
            log.info("[handleOrderTimeout][支付宝订单未支付或查询失败，取消订单，订单号：{}]", order.getOrderNo());
            order.setStatus(OrderStatusEnum.CANCELLED.getCode());
            order.setCancelTime(LocalDateTime.now());
            orderMapper.updateById(order);
            
            // 记录订单超时操作日志
            orderOperationLogService.createOrderOperationLog(
                    order,
                    OrderOperationTypeEnum.TIMEOUT.getCode(),
                    0L,
                    "system",
                    previousStatus,
                    "订单支付超时自动取消"
            );
        }
    }


    @Override
    public AppOrderTradeCreateRespVO payOrderById(Long orderId) {
        OrderDO order = getOrder(orderId);
        if(order == null){
            throw businessException("订单不存在");
        }
        // 如果订单状态不是待支付，抛出异常
        if(!OrderStatusEnum.PENDING.getCode().equals(order.getStatus())){
            throw businessException("订单状态异常，无法支付");
        }
        // 用户信息
        Long orderUserId = order.getUserId();
        MemberDO member = memberRentalService.getMember(orderUserId);
        if(member == null || member.getOpenid() == null){
            throw businessException("用户信息不存在或未绑定小程序");
        }

        // 判断订单类型
        boolean isRentOrder = OrderTypeEnum.RENT.getCode().equals(order.getOrderType());
        boolean isBuyOrder = OrderTypeEnum.BUY.getCode().equals(order.getOrderType());
        
        log.info("[payOrderById][开始处理订单支付，订单号：{}，订单类型：{}]", order.getOrderNo(), 
                isRentOrder ? "租车" : (isBuyOrder ? "购车" : "未知类型"));
        
        AppOrderTradeCreateRespVO resultVO = new AppOrderTradeCreateRespVO();
        
        // 根据订单类型和支付方式处理不同的支付情况
        if (isBuyOrder && PaymentMethodEnum.PREPAY.getCode().equals(order.getRentPaymentMode())) {
            // 购车订单且是先付后用订单
            log.info("[payOrderById][处理购车订单支付，订单号：{}]", order.getOrderNo());
            
            // 获取参数。调用支付宝支付
            String subject = order.getStoreName() + " - " + order.getVehicleName() + " * " + order.getQuantity();
            AlipayTradeCreateDTO alipayTradeCreateDTO = new AlipayTradeCreateDTO();
            alipayTradeCreateDTO.setOutTradeNo(order.getOrderNo());
            alipayTradeCreateDTO.setSubject(subject);
            alipayTradeCreateDTO.setBuyerId(member.getOpenid());
            alipayTradeCreateDTO.setTotalAmount(order.getAmount());
            AlipayTradeCreateRespVO respVO = alipayPayService.createTrade(alipayTradeCreateDTO);

            resultVO.setTradePayString(respVO.getTradePayString());
            resultVO.setTradeStatus(respVO.getTradeStatus());
            resultVO.setTradeNo(respVO.getTradeNo());
            resultVO.setOutTradeNo(respVO.getOutTradeNo());
            
        } else if (isRentOrder) {
            // 租车订单，根据depositType区分处理方式
            if (PaymentMethodEnum.PREPAY.getCode().equals(order.getRentPaymentMode())) {
                // 先付后用，使用普通支付宝支付接口
                log.info("[payOrderById][处理租车订单支付(先付后用)，订单号：{}]", order.getOrderNo());
                
                String subject = order.getStoreName() + " - " + order.getVehicleName() + " - " +
                        RentalPeriodEnum.fromCode(order.getRentMode()).getValue() + "*" + order.getRentDuration();
                
                AlipayTradeCreateDTO alipayTradeCreateDTO = new AlipayTradeCreateDTO();
                alipayTradeCreateDTO.setOutTradeNo(order.getOrderNo());
                alipayTradeCreateDTO.setSubject(subject);
                alipayTradeCreateDTO.setBuyerId(member.getOpenid());
                // 租车订单需要加上押金
                BigDecimal totalAmount = order.getAmount();
                if (order.getDepositAmount() != null) {
                    totalAmount = totalAmount.add(order.getDepositAmount());
                }
                alipayTradeCreateDTO.setTotalAmount(totalAmount);
                AlipayTradeCreateRespVO respVO = alipayPayService.createTrade(alipayTradeCreateDTO);

                resultVO.setTradePayString(respVO.getTradePayString());
                resultVO.setTradeStatus(respVO.getTradeStatus());
                resultVO.setTradeNo(respVO.getTradeNo());
                resultVO.setOutTradeNo(respVO.getOutTradeNo());
                
            } else if (PaymentMethodEnum.POSTPAY.getCode().equals(order.getRentPaymentMode())) {
                // 先用后付，使用芝麻免押创建接口
                log.info("[payOrderById][处理租车订单支付(先用后付)，订单号：{}]", order.getOrderNo());
                
                String subject = order.getStoreName() + " - " + order.getVehicleName() + " - " +
                        RentalPeriodEnum.fromCode(order.getRentMode()).getValue() + "*" + order.getRentDuration();
                
                // 构建芝麻免押参数
                AlipayZmDepositCreditOrderModel model = new AlipayZmDepositCreditOrderModel();
                model.setDepositProductMode("POSTPAY");
                model.setOrderTitle(subject);
                model.setOutOrderNo(order.getOrderNo());
                model.setOutRequestNo(order.getOrderNo());
                model.setProductCode("PRE_AUTH_ONLINE");
                
                // 租车订单需要加上押金
                BigDecimal totalAmount = order.getAmount();
                if (order.getDepositAmount() != null) {
                    totalAmount = totalAmount.add(order.getDepositAmount());
                }
                model.setTotalAmount(totalAmount.toString());
                
                String creditServiceStr = String.format("{\"category\":\"%s\",\"serviceId\":\"%s\"}", properties.getZmCategory(), properties.getZmServiceId());
                model.setCreditServiceStr(creditServiceStr);
                
                AlipayZmDepositPostPaymentsModel paymentsModel = new AlipayZmDepositPostPaymentsModel();
                paymentsModel.setProductModeAmount(totalAmount.toString());
                paymentsModel.setProductModeName(subject);
                model.setPostPaymentsModel(paymentsModel);
                
                String depositOrder = zmDepositPayService.createDepositOrder(model);
                
                resultVO.setTradePayString(depositOrder);
                resultVO.setTradeStatus("WAIT_BUYER_PAY");
                resultVO.setTradeNo(order.getOrderNo());
                resultVO.setOutTradeNo(order.getOrderNo());
            } else {
                // 未知的押金类型
                log.warn("[payOrderById][未知的押金类型，订单号：{}，押金类型：{}]", order.getOrderNo(), order.getDepositType());
                throw businessException("未知的押金类型");
            }
        } else {
            // 未知订单类型
            log.warn("[payOrderById][未知订单类型，订单号：{}]", order.getOrderNo());
            throw businessException("暂不支持的订单类型");
        }
        
        return resultVO;
    }

    @Override
    public void updateOrder(OrderSaveReqVO updateReqVO) {
        // 校验存在
        validateOrderExists(updateReqVO.getId());
        // 更新
        OrderDO updateObj = BeanUtils.toBean(updateReqVO, OrderDO.class);
        orderMapper.updateById(updateObj);
    }

    @Override
    public void updateOrderEntity(OrderDO order) {
        // 校验存在
        validateOrderExists(order.getId());
        orderMapper.updateById(order);
    }

    @Override
    public void deleteOrder(Long id) {
        // 校验存在
        validateOrderExists(id);
        // 删除
        orderMapper.deleteById(id);
    }

    @Override
    public void deleteOrderListByIds(List<Long> ids) {
        // 校验存在
        validateOrderExists(ids);
        // 删除
        orderMapper.deleteByIds(ids);
    }



    @Override
    @Transactional
    public AlipayTradeRefundRespVO refundOrder(OrderRefundReqVO reqVO) {
        OrderDO order = getOrder(reqVO.getId());
        if (order == null) {
            throw businessException("订单不存在");
        }

        // 记录退款前的状态
        String previousStatus = order.getStatus();

        // 订单类型
        boolean isRentOrder = OrderTypeEnum.RENT.getCode().equals(order.getOrderType());
        boolean isBuyOrder = OrderTypeEnum.BUY.getCode().equals(order.getOrderType());
        
        // 2. 校验订单状态
        if (!OrderStatusEnum.COMPLETED.getCode().equals(order.getStatus())) {
            log.info("订单状态不正确，订单号：{}，当前状态：{}]",
                    order.getOrderNo(), order.getStatus());
            throw businessException("订单状态不可退款");
        }

        // 3. 校验退款金额是否合法
        // 3.1 金额是否为正数
        if (!RentalCommonUtils.isValidAmount(reqVO.getRefundAmount())) {
            throw businessException("退款金额必须大于0");
        }

        // 3.2 退款金额不能大于订单金额
        if (reqVO.getRefundAmount().compareTo(order.getAmount()) > 0) {
            throw businessException("退款金额不能大于订单金额");
        }

        // 4. 如果订单已有退款，则累计退款金额不能超过订单金额
        BigDecimal totalRefundAmount = BigDecimal.ZERO;
        if (order.getRefundAmount() != null) {
            totalRefundAmount = order.getRefundAmount();
        }
        totalRefundAmount = totalRefundAmount.add(reqVO.getRefundAmount());
        if (totalRefundAmount.compareTo(order.getAmount()) > 0) {
            throw businessException("总退款金额不能超过订单金额");
        }

        // 生成退款请求号
        String outRequestNo = RentalCommonUtils.generateOrderNumber("RF");
        
        // 5. 根据订单类型和押金类型选择不同的退款接口
        AlipayTradeRefundRespVO refundRespVO = new AlipayTradeRefundRespVO();
        
        if (isRentOrder && PaymentMethodEnum.POSTPAY.getCode().equals(order.getRentPaymentMode())) {
            // 租车订单且为芝麻免押，使用芝麻免押退款接口
            log.info("[refundOrder][租车订单为芝麻免押类型，使用芝麻信用退款接口，订单号：{}]", order.getOrderNo());
            
            Boolean refundResult = zmDepositPayService.refundTrade(
                    order.getOrderNo(),
                    order.getOrderNo(),
                    reqVO.getRefundAmount(),
                    reqVO.getRefundReason()
            );
            
            if (refundResult) {
                // 退款成功，查询退款结果获取详细信息
                AlipayZmDepositRefundRespVO zmRefundRespVO = zmDepositPayService.queryRefund(order.getOrderNo(), outRequestNo);

                // 将芝麻免押退款结果转换为标准退款结果
                refundRespVO.setOutTradeNo(zmRefundRespVO.getOutTradeNo());
                refundRespVO.setTradeNo(order.getAlipayTradeNo());
                refundRespVO.setRefundFee(zmRefundRespVO.getRefundAmount());
                refundRespVO.setGmtRefundPay(new Date());
            } else {
                // 退款失败
                log.error("[refundOrder][芝麻免押退款失败，订单号：{}]", order.getOrderNo());
                throw businessException("芝麻免押退款失败");
            }
        } else {
            // 普通支付宝退款
            log.info("[refundOrder][使用普通支付宝退款接口，订单号：{}]", order.getOrderNo());
            
            AlipayTradeRefundDTO refundDTO = new AlipayTradeRefundDTO();
            refundDTO.setOutTradeNo(order.getOrderNo());
            refundDTO.setTradeNo(order.getAlipayTradeNo());
            refundDTO.setRefundAmount(reqVO.getRefundAmount());
            refundDTO.setRefundReason(reqVO.getRefundReason());
            refundDTO.setOutRequestNo(outRequestNo);
            
            // 调用支付宝退款接口
            refundRespVO = alipayPayService.refundTrade(refundDTO);
        }

        // 7. 更新订单的退款金额
        order.setRefundAmount(totalRefundAmount);
        order.setRefundReason(reqVO.getRefundReason());
        order.setRefundTime(LocalDateTime.now());
        order.setStatus(OrderStatusEnum.REFUND.getCode());
        orderMapper.updateById(order);

        // 8. 记录订单退款操作日志
        String orderTypeDesc = isRentOrder ? "租车" : (isBuyOrder ? "购车" : "未知类型");
        orderOperationLogService.createOrderOperationLog(
                order,
                OrderOperationTypeEnum.REFUND.getCode(),
                SecurityFrameworkUtils.getLoginUserId(),
                "admin",
                previousStatus,
                orderTypeDesc + "订单退款：" + reqVO.getRefundReason()
        );

        return refundRespVO;
    }

    @Override
    public List<OrderDO> getTimeoutPendingOrders(int minutes) {
        // 计算超时时间点
        LocalDateTime timeoutTime = LocalDateTime.now().minusMinutes(minutes);

        // 查询创建时间早于超时时间点且状态为待支付的订单
        return orderMapper.selectList(new LambdaQueryWrapper<OrderDO>()
                .eq(OrderDO::getStatus, OrderStatusEnum.PENDING.getCode())
                .lt(OrderDO::getCreateTime, timeoutTime));
    }

    @Override
    public Boolean updateOrderFinishStatus(String orderNo) {
        OrderDO order = getOrderByOrderNo(orderNo);
        if (order == null) {
            throw businessException("订单不存在");
        }

        // 记录完成前的状态
        String previousStatus = order.getStatus();

        Boolean isRentOrder = OrderTypeEnum.RENT.getCode().equals(order.getOrderType());
        Boolean isBuyOrder = OrderTypeEnum.BUY.getCode().equals(order.getOrderType());

        // 根据订单类型检查状态是否允许完成
        if (isRentOrder) {
            // 租车订单，状态应为IN_PROGRESS
            if (!OrderStatusEnum.IN_PROGRESS.getCode().equals(order.getStatus())) {
                log.info("[updateOrderFinishStatus][租车订单状态不允许完成，订单号：{}，当前状态：{}]", 
                        order.getOrderNo(), order.getStatus());
                throw businessException("订单状态不允许完成");
            }
        } else if (isBuyOrder) {
            // 购车订单，状态应为PAID
            if (!OrderStatusEnum.PAID.getCode().equals(order.getStatus())) {
                log.info("[updateOrderFinishStatus][购车订单状态不允许完成，订单号：{}，当前状态：{}]", 
                        order.getOrderNo(), order.getStatus());
                throw businessException("订单状态不允许完成");
            }
        } else {
            // 未知订单类型
            log.warn("[updateOrderFinishStatus][未知订单类型，不允许完成，订单号：{}]", order.getOrderNo());
            throw businessException("未知订单类型");
        }

        // 对于租车订单且为芝麻免押（先用后付），需要调用扣款接口
        if (isRentOrder && PaymentMethodEnum.POSTPAY.getCode().equals(order.getRentPaymentMode())) {
            log.info("[updateOrderFinishStatus][租车订单为芝麻免押类型，调用扣款接口，订单号：{}]", orderNo);
            String subject = order.getStoreName() + " - " + order.getVehicleName() + " - "+RentalPeriodEnum.fromCode(order.getRentMode()).getValue() + "*" + order.getRentDuration();
            // 构建扣款参数
            AlipayZmDepositTradePayDTO payDTO = new AlipayZmDepositTradePayDTO();
            payDTO.setOutTradeNo(orderNo);
            payDTO.setSubject(subject);
            payDTO.setTotalAmount(order.getAmount().toString());
            payDTO.setAuthNo(order.getAlipayTradeNo());
            payDTO.setProductCode("PRE_AUTH_ONLINE");
            payDTO.setAuthConfirmMode("COMPLETE"); // 转交易完成后解冻剩余冻结金额
            
            // 调用芝麻免押扣款接口
            Boolean payResult = zmDepositPayService.tradePay(payDTO);
            
            if (!payResult) {
                // 扣款失败
                log.error("[updateOrderFinishStatus][芝麻免押扣款失败，订单号：{}]", orderNo);
                throw businessException("芝麻免押扣款失败，无法完成订单");
            }
            
            log.info("[updateOrderFinishStatus][芝麻免押扣款成功，订单号：{}]", orderNo);
            order.setRentDepositRefundStatus("Y");
            order.setRentDepositRefundTime(LocalDateTime.now());
        } else if (isRentOrder && PaymentMethodEnum.PREPAY.getCode().equals(order.getRentPaymentMode())) {
            // 如果存在押金，需要退款押金金额
            if (order.getDepositAmount() != null && order.getDepositAmount().compareTo(BigDecimal.ZERO) > 0) {
                // 调用支付宝退款接口
                AlipayTradeRefundDTO refundDTO = new AlipayTradeRefundDTO();
                refundDTO.setOutTradeNo(order.getOrderNo());
                refundDTO.setTradeNo(order.getAlipayTradeNo());
                refundDTO.setRefundAmount(order.getDepositAmount());
                refundDTO.setRefundReason("租车订单完成，退还押金");
                refundDTO.setOutRequestNo(RentalCommonUtils.generateOrderNumber("RF"));

                AlipayTradeRefundRespVO refundRespVO = alipayPayService.refundTrade(refundDTO);
                if (refundRespVO == null) {
                    log.error("[updateOrderFinishStatus][退还租车押金失败，订单号：{}]", orderNo);
                    throw businessException("退还租车押金失败，无法完成订单");
                }
                log.info("[updateOrderFinishStatus][退还租车押金成功，订单号：{}]", orderNo);
                order.setRentDepositRefundStatus("Y");
                order.setRentDepositRefundTime(LocalDateTime.now());
            }
        }

        // 更新订单状态
        order.setStatus(OrderStatusEnum.COMPLETED.getCode());
        order.setFinishTime(LocalDateTime.now());
        orderMapper.updateById(order);

        // 记录订单完成操作日志
        String orderTypeDesc = isRentOrder ? "租车" : (isBuyOrder ? "购车" : "未知类型");
        orderOperationLogService.createOrderOperationLog(
                order,
                OrderOperationTypeEnum.FINISH.getCode(),
                SecurityFrameworkUtils.getLoginUserId(),
                "user",
                previousStatus,
                orderTypeDesc + "订单完成"
        );

        return true;
    }

    @Override
    public OrderDO findOrderByOrderNo(String orderNo) {
        return orderMapper.selectOne(new LambdaQueryWrapper<OrderDO>().eq(OrderDO::getOrderNo, orderNo));
    }
    
    @Override
    public List<OrderDO> getPaidBuyOrdersOlderThanOneMonth() {
        // 计算一个月前的时间点
        LocalDateTime oneMonthAgo = LocalDateTime.now().minusMonths(1);
        
        // 查询订单类型为购车、状态为已支付、支付时间早于一个月前的订单
        return orderMapper.selectList(new LambdaQueryWrapper<OrderDO>()
                .eq(OrderDO::getOrderType, OrderTypeEnum.BUY.getCode())
                .eq(OrderDO::getStatus, OrderStatusEnum.PAID.getCode())
                .isNotNull(OrderDO::getPayTime)
                .lt(OrderDO::getPayTime, oneMonthAgo));
    }

    @Override
    public Boolean updateBuyOrderToCompleted(String orderNo) {
        // 查询订单
        OrderDO order = getOrderByOrderNo(orderNo);
        if (order == null) {
            log.error("[updateBuyOrderToCompleted][订单不存在，订单号：{}]", orderNo);
            throw businessException("订单不存在");
        }

        // 验证是否为购车订单
        if (!OrderTypeEnum.BUY.getCode().equals(order.getOrderType())) {
            log.error("[updateBuyOrderToCompleted][非购车订单，无法处理，订单号：{}，订单类型：{}]",
                    orderNo, order.getOrderType());
            throw businessException("非购车订单，无法处理");
        }

        // 验证订单状态是否为已支付
        if (!OrderStatusEnum.PAID.getCode().equals(order.getStatus())) {
            log.error("[updateBuyOrderToCompleted][购车订单状态不是已支付，无法处理，订单号：{}，当前状态：{}]",
                    orderNo, order.getStatus());
            throw businessException("订单状态不是已支付，无法处理");
        }

        // 记录完成前的状态
        String previousStatus = order.getStatus();

        // 更新订单状态为已完成
        order.setStatus(OrderStatusEnum.COMPLETED.getCode());
        order.setFinishTime(LocalDateTime.now());
        orderMapper.updateById(order);

        // 记录订单完成操作日志
        orderOperationLogService.createOrderOperationLog(
                order,
                OrderOperationTypeEnum.FINISH.getCode(),
                0L, // 系统操作
                "system",
                previousStatus,
                "购车订单自动完成（超过一个月）"
        );

        log.info("[updateBuyOrderToCompleted][购车订单已自动完成，订单号：{}]", orderNo);
        return true;
    }

    @Override
    public List<OrderDO> getInProgressRentOrders() {
        // 查询订单类型为租车、状态为进行中的订单
        return orderMapper.selectList(new LambdaQueryWrapper<OrderDO>()
                .eq(OrderDO::getOrderType, OrderTypeEnum.RENT.getCode())
                .eq(OrderDO::getStatus, OrderStatusEnum.IN_PROGRESS.getCode()));
    }
    
    @Override
    public Boolean updateRentOrderToCompleted(String orderNo) {
        OrderDO order = getOrderByOrderNo(orderNo);
        if (order == null) {
            log.error("[updateRentOrderToCompleted][订单不存在，订单号：{}]", orderNo);
            return false;
        }
        
        // 记录完成前的状态
        String previousStatus = order.getStatus();
        
        // 验证是否为租车订单
        if (!OrderTypeEnum.RENT.getCode().equals(order.getOrderType())) {
            log.error("[updateRentOrderToCompleted][非租车订单，无法处理，订单号：{}，订单类型：{}]", 
                    orderNo, order.getOrderType());
            return false;
        }
        
        // 验证订单状态是否为进行中
        if (!OrderStatusEnum.IN_PROGRESS.getCode().equals(order.getStatus())) {
            log.error("[updateRentOrderToCompleted][租车订单状态不是进行中，无法完成，订单号：{}，当前状态：{}]", 
                    orderNo, order.getStatus());
            return false;
        }
        
        // 对于租车订单且为芝麻免押（先用后付），需要调用扣款接口
        if (PaymentMethodEnum.POSTPAY.getCode().equals(order.getRentPaymentMode())) {
            log.info("[updateRentOrderToCompleted][租车订单为芝麻免押类型，调用扣款接口，订单号：{}]", orderNo);
            String subject = order.getStoreName() + " - " + order.getVehicleName() + " - "+RentalPeriodEnum.fromCode(order.getRentMode()).getValue() + "*" + order.getRentDuration();
            // 构建扣款参数
            AlipayZmDepositTradePayDTO payDTO = new AlipayZmDepositTradePayDTO();
            payDTO.setOutTradeNo(orderNo);
            payDTO.setSubject(subject);
            payDTO.setTotalAmount(order.getAmount().toString());
            payDTO.setAuthNo(order.getAlipayTradeNo());
            payDTO.setProductCode("PRE_AUTH_ONLINE");
            payDTO.setAuthConfirmMode("COMPLETE"); // 转交易完成后解冻剩余冻结金额
            
            // 调用芝麻免押扣款接口
            Boolean payResult = zmDepositPayService.tradePay(payDTO);
            
            if (!payResult) {
                // 扣款失败
                log.error("[updateRentOrderToCompleted][芝麻免押扣款失败，订单号：{}]", orderNo);
                return false;
            }
            
            log.info("[updateRentOrderToCompleted][芝麻免押扣款成功，订单号：{}]", orderNo);
        }
        
        // 更新订单状态
        order.setStatus(OrderStatusEnum.COMPLETED.getCode());
        order.setFinishTime(LocalDateTime.now());
        orderMapper.updateById(order);
        
        // 记录订单完成操作日志（使用系统用户）
        orderOperationLogService.createOrderOperationLog(
                order,
                OrderOperationTypeEnum.FINISH.getCode(),
                0L, // 系统操作
                "system",
                previousStatus,
                "租车订单自动完成（到期）"
        );
        
        log.info("[updateRentOrderToCompleted][租车订单已自动完成，订单号：{}]", orderNo);
        return true;
    }
}
