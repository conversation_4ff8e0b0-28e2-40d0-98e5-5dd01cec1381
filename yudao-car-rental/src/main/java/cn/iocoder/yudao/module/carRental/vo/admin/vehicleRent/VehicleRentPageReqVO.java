package cn.iocoder.yudao.module.carRental.vo.admin.vehicleRent;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 租赁车辆分页 Request VO")
@Data
public class VehicleRentPageReqVO extends PageParam {

    @Schema(description = "车辆编号（平台内部唯一标识）")
    private String vehicleNo;


    @Schema(description = "车辆名称）")
    private String name;

    @Schema(description = "车牌号")
    private String licensePlate;

    @Schema(description = "车架号（VIN）")
    private String vin;

    @Schema(description = "所属门店ID（逻辑关联 rental_store.id）", example = "28651")
    private Long storeId;

    @Schema(description = "品牌ID")
    private Long brandId;

    @Schema(description = "型号")
    private String model;

    @Schema(description = "车辆详细说明（如颜色、配置等）")
    private String detail;


    /**
     * 时租
     */
    @Schema(description = "时租价格（单位：元/小时）", example = "20184")
    private BigDecimal hourPrice;

    /**
     * 日租
     */
    @Schema(description = "日租价格（单位：元/天）", example = "20184")
    private BigDecimal dayPrice;

    /**
     * 月租
     */
    @Schema(description = "月租价格（单位：元/月）", example = "20184")
    private BigDecimal monthPrice;

    @Schema(description = "车辆图片，JSON数组格式（['url1', 'url2', ...]）")
    private String images;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "状态（1=上架，0=下架，2=出售）", example = "1")
    private Integer status;


    @Schema(description = "押金", example = "1000")
    private BigDecimal deposit;

}