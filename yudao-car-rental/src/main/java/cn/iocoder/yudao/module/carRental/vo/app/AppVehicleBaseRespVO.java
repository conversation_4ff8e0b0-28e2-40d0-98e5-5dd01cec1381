package cn.iocoder.yudao.module.carRental.vo.app;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/7/26 23:23
 * @description
 */
@Schema(description = "用户 APP  - 车辆信息Resp VO")
@Data
public class AppVehicleBaseRespVO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "车辆编号（平台内部唯一标识）")
    private String vehicleNo;


    @Schema(description = "车牌号")
    private String licensePlate;

    @Schema(description = "图片")
    private String images;

    @Schema(description = "品牌")
    private Long brandId;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "型号")
    private String model;

    @Schema(description = "车类型")
    private String type;

    @Schema(description = "价格（购车）")
    private BigDecimal price;


    @Schema(description = "押金(租车)")
    private BigDecimal deposit;
    @Schema(description = "时租(租车)")
    private BigDecimal hourPrice;
    @Schema(description = "日租(租车)")
    private BigDecimal dayPrice;
    @Schema(description = "月租(租车)")
    private BigDecimal monthPrice;


}
