package cn.iocoder.yudao.module.carRental.dal.dataobject;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * 租车门店 DO
 *
 * <AUTHOR>
 */
@TableName("rental_store")
@KeySequence("rental_store_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TenantIgnore
public class StoreDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 门店名称
     */
    private String name;
    /**
     * 门店地址
     */
    private String address;
    /**
     * 经度
     */
    private BigDecimal longitude;
    /**
     * 纬度
     */
    private BigDecimal latitude;
    /**
     * 营业时间（如 10:00-18:00）
     */
    private String businessHours;
    /**
     * 是否支持到店取车（1=是, 0=否）
     */
    private Boolean supportPickup;
    /**
     * 是否支持送车上门（1=是, 0=否）
     */
    private Boolean supportDelivery;
    /**
     * 联系电话
     */
    private String phone;
    /**
     * 门店状态（1=营业中，0=已停业）
     */
    private Boolean status;

    /**
     * 排序规则
     */
    private String sortRule;



}