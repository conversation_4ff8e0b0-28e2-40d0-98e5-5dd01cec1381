package cn.iocoder.yudao.module.carRental.dto;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025-07-19 17:25
 * @description
 */
@Schema(description = "用户 APP - 租车、购车列表 DTO")
@Data
public class AppVehicleSimpleDTO extends PageParam {

    @Schema(description = "品牌ID")
    private Long brandId;
    @Schema(description = "价格(asc、desc)")
    private String priceSort;

    @Schema(description = "门店ID")
    private Long storeId;

    @Schema(description = "排序规则(车辆名称，用逗号分隔，如：雅迪小新,小牛速跑)")
    private String sortRule;

}
