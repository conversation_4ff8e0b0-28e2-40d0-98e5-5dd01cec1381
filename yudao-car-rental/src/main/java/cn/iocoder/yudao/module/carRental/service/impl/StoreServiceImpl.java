package cn.iocoder.yudao.module.carRental.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.carRental.constant.StoreGeoConst;
import cn.iocoder.yudao.module.carRental.dal.dataobject.*;
import cn.iocoder.yudao.module.carRental.dal.mysql.StoreImageMapper;
import cn.iocoder.yudao.module.carRental.dal.mysql.StoreMapper;
import cn.iocoder.yudao.module.carRental.dto.AppStoreNearbyDTO;
import cn.iocoder.yudao.module.carRental.dto.AppVehicleSimpleDTO;
import cn.iocoder.yudao.module.carRental.service.*;
import cn.iocoder.yudao.module.carRental.utils.QrCodeWithLogoUtils;
import cn.iocoder.yudao.module.carRental.vo.admin.common.RentalBaseCommonRespVO;
import cn.iocoder.yudao.module.carRental.vo.admin.store.StorePageReqVO;
import cn.iocoder.yudao.module.carRental.vo.admin.store.StoreSaveReqVO;
import cn.iocoder.yudao.module.carRental.vo.admin.vehicleRent.VehicleRentPageReqVO;
import cn.iocoder.yudao.module.carRental.vo.admin.vehicleRent.VehicleRentRespVO;
import cn.iocoder.yudao.module.carRental.vo.admin.vehicleSale.VehicleSalePageReqVO;
import cn.iocoder.yudao.module.carRental.vo.admin.vehicleSale.VehicleSaleRespVO;
import cn.iocoder.yudao.module.carRental.vo.app.AppBrandListVO;
import cn.iocoder.yudao.module.carRental.vo.app.AppStoreNearbyRespVO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.geo.*;
import org.springframework.data.redis.connection.RedisGeoCommands;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.domain.geo.Metrics;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.businessException;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.diffList;

/**
 * 租车门店 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class StoreServiceImpl implements StoreService {

    @Resource
    private StoreMapper storeMapper;
    @Resource
    private StoreImageMapper storeImageMapper;

    @Autowired
    private StoreGeoService storeGeoService;

    @Autowired
    private VehicleRentService rentService;

    @Autowired
    private VehicleSaleService saleService;

    @Autowired
    private BrandService brandService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createStore(StoreSaveReqVO createReqVO) {
        // 插入
        StoreDO store = BeanUtils.toBean(createReqVO, StoreDO.class);
        storeMapper.insert(store);

        // 插入子表
        createStoreImageList(store.getId(), createReqVO.getStoreImages());

        // 写入 Redis GEO
        if(createReqVO.getStatus() != null && createReqVO.getStatus() ==1){
            storeGeoService.saveOrUpdateGeo(store.getId(), store.getLongitude(), store.getLatitude());
        }
        // 返回
        return store.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStore(StoreSaveReqVO updateReqVO) {
        // 校验存在
        validateStoreExists(updateReqVO.getId());
        // 更新
        StoreDO updateObj = BeanUtils.toBean(updateReqVO, StoreDO.class);
        storeMapper.updateById(updateObj);

        // 更新子表
        updateStoreImageList(updateReqVO.getId(), updateReqVO.getStoreImages());

        // 更新 Redis GEO
        if(updateReqVO.getStatus() != null && updateReqVO.getStatus() == 1){
            storeGeoService.saveOrUpdateGeo(updateObj.getId(), updateObj.getLongitude(), updateObj.getLatitude());
        } else {
            // 如果门店状态为非营业，则删除 Redis GEO
            storeGeoService.deleteGeo(updateObj.getId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteStore(Long id) {
        // 校验存在
        validateStoreExists(id);
        // 删除
        storeMapper.deleteById(id);

        // 删除子表
        deleteStoreImageByStoreId(id);

        // 删除 Redis GEO
        storeGeoService.deleteGeo(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteStoreListByIds(List<Long> ids) {
        // 校验存在
        validateStoreExists(ids);
        // 删除
        storeMapper.deleteByIds(ids);

        // 删除子表
        deleteStoreImageByStoreIds(ids);

        // 批量删除 Redis GEO
        storeGeoService.deleteGeoBatch(ids);
    }

    private void validateStoreExists(List<Long> ids) {
        List<StoreDO> list = storeMapper.selectByIds(ids);
        if (CollUtil.isEmpty(list) || list.size() != ids.size()) {
            throw businessException("租车门店不存在");
        }
    }

    private void validateStoreExists(Long id) {
        if (storeMapper.selectById(id) == null) {
            throw businessException("租车门店不存在");
        }
    }

    @Override
    public StoreDO getStore(Long id) {
        return storeMapper.selectById(id);
    }

    @Override
    public PageResult<StoreDO> getStorePage(StorePageReqVO pageReqVO) {
        return storeMapper.selectPage(pageReqVO);
    }



    @Override
    public List<AppStoreNearbyRespVO> findNearbyStores(AppStoreNearbyDTO dto) {
        log.info("查询附近门店，中心点：longitude={}, latitude={}, radius={}米",
                dto.getLongitude(), dto.getLatitude(), dto.getRadius());

        if (dto.getLongitude() == null || dto.getLatitude() == null || dto.getRadius() == null) {
            log.warn("传入的坐标或半径为空，直接返回空结果");
            return Collections.emptyList();
        }

        Point center = new Point(dto.getLongitude(), dto.getLatitude());
        Distance distance = new Distance(dto.getRadius(), Metrics.METERS);
        Circle within = new Circle(center, distance);

        GeoResults<RedisGeoCommands.GeoLocation<String>> geoResults;
        try {
            geoResults = stringRedisTemplate.opsForGeo().radius(
                    StoreGeoConst.STORE_GEO_KEY,
                    within,
                    RedisGeoCommands.GeoRadiusCommandArgs.newGeoRadiusArgs()
                            .includeDistance()
                            .sortAscending()
            );
        } catch (Exception e) {
            log.error("Redis GEO 查询出错，可能是 ZSet 成员异常：{}", e.getMessage(), e);
            return Collections.emptyList();
        }


        if (geoResults == null || geoResults.getContent().isEmpty()) {
            log.info("未查到任何门店，可能范围内无门店");
            return Collections.emptyList();
        }

        List<Long> storeIds = new ArrayList<>();
        for (GeoResult<RedisGeoCommands.GeoLocation<String>> result : geoResults.getContent()) {
            String member = result.getContent().getName();
            if (StrUtil.isBlank(member)) continue;
            try {
                storeIds.add(Long.valueOf(member));
            } catch (NumberFormatException ex) {
                log.warn("非法 GEO 成员值：{}，跳过", member);
            }
        }

        if (storeIds.isEmpty()) {
            log.warn("GEO 查询结果中没有合法的门店 ID");
            return Collections.emptyList();
        }

        List<StoreDO> storeList = storeMapper.selectBatchIds(storeIds);
        if (CollectionUtils.isEmpty(storeList)) {
            log.warn("数据库中未找到门店信息，storeIds={}", storeIds);
            return Collections.emptyList();
        }

        Map<Long, StoreDO> storeMap = storeList.stream()
                .collect(Collectors.toMap(StoreDO::getId, Function.identity()));

        List<AppStoreNearbyRespVO> result = new ArrayList<>();
        for (GeoResult<RedisGeoCommands.GeoLocation<String>> item : geoResults.getContent()) {
            String member = item.getContent().getName();
            if (StrUtil.isBlank(member)) continue;
            Long id;
            try {
                id = Long.valueOf(member);
            } catch (NumberFormatException e) {
                log.warn("跳过非法 ID：{}", member);
                continue;
            }

            StoreDO store = storeMap.get(id);
            if (store == null) continue;

            AppStoreNearbyRespVO vo = new AppStoreNearbyRespVO();
            vo.setId(store.getId());
            vo.setName(store.getName());
            vo.setAddress(store.getAddress());
            vo.setPhone(store.getPhone());
            vo.setBusinessHours(store.getBusinessHours());
            vo.setSupportDelivery(store.getSupportDelivery());
            vo.setCreateTime(store.getCreateTime());

            List<StoreImageDO> images = getStoreImageListByStoreId(id);
            if (!CollectionUtils.isEmpty(images)) {
                vo.setUrl(images.get(0).getImageUrl());
            }

            BigDecimal minPrice = rentService.findMinPriceVehicleByStoreId(id);
            vo.setMiniPrice(minPrice);

            // 设置租车数量
            Long vehicleRentCount = rentService.countVehicleRentByStoreId(id);
            vo.setVehicleRentCount(vehicleRentCount);

            result.add(vo);
        }

        log.info("附近门店查询完成，返回数量：{}", result.size());
        return result;
    }





    @Override
    public List<AppBrandListVO> findStoreBrands(Long storeId,String type) {
        if(StrUtil.isEmpty(type)){
            throw businessException("类型不能为空");
        }

        List<Long> brandIds = new ArrayList<>();
        if("1".equals(type)){
            // 获取租车里面的品牌ID
            brandIds = rentService.findBrandIdsListByStoreId(storeId);
        } else if("2".equals(type)){
            brandIds = saleService.findBrandIdsListByStoreId(storeId);
        } else {
            throw businessException("非法类型");
        }

        // 根据branIds 获取详细信息
        if(CollUtil.isEmpty(brandIds)){
            return Collections.emptyList();
        }
        List<BrandDO> brandListByIds = brandService.findBrandListByIds(brandIds);
        return BeanUtils.toBean(brandListByIds, AppBrandListVO.class);
    }

    @Override
    public PageResult<VehicleRentRespVO> findAppLeaseCarPageList(AppVehicleSimpleDTO dto) {
        // 判断一下门店是否存在
        StoreDO storeDO = storeMapper.selectById(dto.getStoreId());
        if ( storeDO == null) {
            throw businessException("租车门店不存在");
        }
        VehicleRentPageReqVO reqVO = new VehicleRentPageReqVO();
        reqVO.setStoreId(storeDO.getId());
        reqVO.setBrandId(dto.getBrandId());
        reqVO.setStatus(1);
        // 排序规则 (雅迪小新,小牛速跑)
        String sortRule = dto.getPriceSort();
        PageResult<VehicleRentDO> pageResult = rentService.getVehicleRentPage(reqVO);
        PageResult<VehicleRentRespVO> vehicleRentRespVOPageResult = BeanUtils.toBean(pageResult, VehicleRentRespVO.class);
        if(!CollectionUtils.isEmpty(vehicleRentRespVOPageResult.getList())){
            // 获取所有的品牌
            List<AppBrandListVO> brandList = brandService.findBrandList();
            // 转为Map
            Map<Long, AppBrandListVO> brandMap = brandList.stream()
                    .collect(Collectors.toMap(AppBrandListVO::getId, Function.identity()));
            // 获取品牌名称
            vehicleRentRespVOPageResult.getList().stream().map(page -> {
                if (brandMap.containsKey(page.getBrandId())) {
                    AppBrandListVO appBrandListVO = brandMap.get(page.getBrandId());
                    page.setBrandName(appBrandListVO.getName());
                }
                return page;
            }).collect(Collectors.toList());
        }
        return vehicleRentRespVOPageResult;
    }

    @Override
    public PageResult<VehicleSaleRespVO> findAppCarPageList(AppVehicleSimpleDTO dto) {
        // 判断一下门店是否存在
        validateStoreExists(dto.getStoreId());
        VehicleSalePageReqVO reqVO = new VehicleSalePageReqVO();
        reqVO.setStoreId(dto.getStoreId());
        reqVO.setBrandId(dto.getBrandId());
        reqVO.setStatus(1);
        PageResult<VehicleSaleDO> pageResult = saleService.getVehicleSalePage(reqVO);
        PageResult<VehicleSaleRespVO> vehicleRentRespVOPageResult = BeanUtils.toBean(pageResult, VehicleSaleRespVO.class);
        if(!CollectionUtils.isEmpty(vehicleRentRespVOPageResult.getList())){
            // 获取所有的品牌
            List<AppBrandListVO> brandList = brandService.findBrandList();
            // 转为Map
            Map<Long, AppBrandListVO> brandMap = brandList.stream()
                    .collect(Collectors.toMap(AppBrandListVO::getId, Function.identity()));
            // 获取品牌名称
            vehicleRentRespVOPageResult.getList().stream().map(page -> {
                if (brandMap.containsKey(page.getBrandId())) {
                    AppBrandListVO appBrandListVO = brandMap.get(page.getBrandId());
                    page.setBrandName(appBrandListVO.getName());
                }
                return page;
            }).collect(Collectors.toList());
        }
        return vehicleRentRespVOPageResult;
    }

    @Override
    public void generateStoreQrCode(Long storeId, HttpServletResponse response) {
        validateStoreExists(storeId);
        StoreDO store = getStore(storeId);
        // 小程序门店路径
        String content = "/pages/pageCar/storeDeatil/index?storeId=" + store.getId();

        // 2. 下载 logo 图片
        BufferedImage logoImage = null;
        List<StoreImageDO> images = getStoreImageListByStoreId(store.getId());
        if (!CollectionUtils.isEmpty(images)) {
            try (InputStream in = new URL(images.get(0).getImageUrl()).openStream()) {
                logoImage = ImageIO.read(in);
            } catch (IOException e) {
                // 无logo也可生成二维码
                logoImage = null;
            }
        }

        // 3. 生成二维码
        try {
            BufferedImage qrImage = null;
            if(logoImage == null){
                qrImage = QrCodeWithLogoUtils.generateQRCode(content, 300, 300);
            } else {
                qrImage = QrCodeWithLogoUtils.generateQRCodeWithLogo(content, 300, 300, logoImage);
            }
            // 4. 输出图片
            response.setContentType(MediaType.IMAGE_PNG_VALUE);
            OutputStream os = response.getOutputStream();
            ImageIO.write(qrImage, "png", os);
            os.flush();
        } catch (Exception e) {
            log.error("生成二维码失败，storeId={}, error={}", storeId, e.getMessage(), e);
            throw  businessException("生成二维码失败，请稍后重试！");
        }
    }

    @Override
    public List<RentalBaseCommonRespVO> findStoreList() {
        List<StoreDO> storeDOS = storeMapper.selectList();
        if (CollUtil.isEmpty(storeDOS)) {
            return Collections.emptyList();
        }
        List<RentalBaseCommonRespVO> list = BeanUtils.toBean(storeDOS, RentalBaseCommonRespVO.class);
        return list;
    }


    // ==================== 子表（门店图片） ====================

    @Override
    public List<StoreImageDO> getStoreImageListByStoreId(Long storeId) {
        List<StoreImageDO> storeImageDOS = storeImageMapper.selectListByStoreId(storeId);
        // 排序（值越小越靠前）
        storeImageDOS.sort((o1, o2) -> ObjectUtil.compare(o1.getSortOrder(), o2.getSortOrder()));
        return storeImageDOS;
    }


    private void createStoreImageList(Long storeId, List<StoreImageDO> list) {
        list.forEach(o -> o.setStoreId(storeId).clean());
        storeImageMapper.insertBatch(list);
    }

    private void updateStoreImageList(Long storeId, List<StoreImageDO> list) {
        list.forEach(o -> o.setStoreId(storeId).clean());
        List<StoreImageDO> oldList = storeImageMapper.selectListByStoreId(storeId);
        List<List<StoreImageDO>> diffList = diffList(oldList, list, (oldVal, newVal) -> {
            boolean same = ObjectUtil.equal(oldVal.getId(), newVal.getId());
            if (same) {
                newVal.setId(oldVal.getId()).clean(); // 解决更新情况下：updateTime 不更新
            }
            return same;
        });

        // 第二步，批量添加、修改、删除
        if (CollUtil.isNotEmpty(diffList.get(0))) {
            storeImageMapper.insertBatch(diffList.get(0));
        }
        if (CollUtil.isNotEmpty(diffList.get(1))) {
            storeImageMapper.updateBatch(diffList.get(1));
        }
        if (CollUtil.isNotEmpty(diffList.get(2))) {
            storeImageMapper.deleteByIds(convertList(diffList.get(2), StoreImageDO::getId));
        }
    }

    private void deleteStoreImageByStoreId(Long storeId) {
        storeImageMapper.deleteByStoreId(storeId);
    }

    private void deleteStoreImageByStoreIds(List<Long> storeIds) {
        storeImageMapper.deleteByStoreIds(storeIds);
    }

}