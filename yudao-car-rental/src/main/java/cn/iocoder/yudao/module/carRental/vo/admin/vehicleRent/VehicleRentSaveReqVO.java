package cn.iocoder.yudao.module.carRental.vo.admin.vehicleRent;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 租赁车辆新增/修改 Request VO")
@Data
public class VehicleRentSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23346")
    private Long id;


    @Schema(description = "车辆名称")
    private String name;

    @Schema(description = "车辆编号（平台内部唯一标识）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "车辆编号（平台内部唯一标识）不能为空")
    private String vehicleNo;

    @Schema(description = "车牌号")
    private String licensePlate;

    @Schema(description = "车架号（VIN）")
    private String vin;

    @Schema(description = "所属门店ID（逻辑关联 rental_store.id）", requiredMode = Schema.RequiredMode.REQUIRED, example = "28651")
    @NotNull(message = "所属门店ID（逻辑关联 rental_store.id）不能为空")
    private Long storeId;

    @Schema(description = "品牌ID")
    private Long brandId;

    @Schema(description = "型号")
    private String model;

    @Schema(description = "车辆详细说明（如颜色、配置等）")
    private String detail;

    /**
     * 时租
     */
    @Schema(description = "时租价格（单位：元/小时）", example = "20184")
    @NotNull(message = "时租价格（单位：元/小时）不能为空")
    private BigDecimal hourPrice;

    /**
     * 日租
     */
    @Schema(description = "日租价格（单位：元/天）", example = "20184")
    @NotNull(message = "日租价格（单位：元/天）不能为空")
    private BigDecimal dayPrice;

    /**
     * 月租
     */
    @Schema(description = "月租价格（单位：元/月）", example = "20184")
    @NotNull(message = "月租价格（单位：元/月）不能为空")
    private BigDecimal monthPrice;

    @Schema(description = "车辆图片，JSON数组格式（['url1', 'url2', ...]）")
    private String images;


    @Schema(description = "状态（1=上架，0=下架，2=出售）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态（1=上架，0=下架，2=出售）不能为空")
    private Integer status;

    @Schema(description = "押金", example = "1000")
    private BigDecimal deposit;
}