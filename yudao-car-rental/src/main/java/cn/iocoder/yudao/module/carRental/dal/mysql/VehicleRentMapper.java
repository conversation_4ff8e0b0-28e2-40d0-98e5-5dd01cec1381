package cn.iocoder.yudao.module.carRental.dal.mysql;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.carRental.dal.dataobject.VehicleRentDO;
import cn.iocoder.yudao.module.carRental.vo.admin.vehicleRent.VehicleRentPageReqVO;
import org.apache.ibatis.annotations.Mapper;
/**
 * 租赁车辆 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface VehicleRentMapper extends BaseMapperX<VehicleRentDO> {

    default PageResult<VehicleRentDO> selectPage(VehicleRentPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<VehicleRentDO>()
                .eqIfPresent(VehicleRentDO::getVehicleNo, reqVO.getVehicleNo())
                .likeIfPresent(VehicleRentDO::getName, reqVO.getName())
                .eqIfPresent(VehicleRentDO::getLicensePlate, reqVO.getLicensePlate())
                .eqIfPresent(VehicleRentDO::getVin, reqVO.getVin())
                .eqIfPresent(VehicleRentDO::getStoreId, reqVO.getStoreId())
                .eqIfPresent(VehicleRentDO::getBrandId, reqVO.getBrandId())
                .eqIfPresent(VehicleRentDO::getModel, reqVO.getModel())
                .eqIfPresent(VehicleRentDO::getDetail, reqVO.getDetail())
                .eqIfPresent(VehicleRentDO::getHourPrice, reqVO.getHourPrice())
                .eqIfPresent(VehicleRentDO::getDayPrice, reqVO.getDayPrice())
                .eqIfPresent(VehicleRentDO::getMonthPrice, reqVO.getMonthPrice())
                .eqIfPresent(VehicleRentDO::getImages, reqVO.getImages())
                .betweenIfPresent(VehicleRentDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(VehicleRentDO::getStatus, reqVO.getStatus())
                .orderByDesc(VehicleRentDO::getId));
    }

}