package cn.iocoder.yudao.module.carRental.utils;

import cn.iocoder.yudao.module.carRental.dal.dataobject.VehicleRentDO;
import cn.iocoder.yudao.module.carRental.enums.RentalPeriodEnum;

import java.math.BigDecimal;

/**
 * 租赁费用计算工具类
 */
public class RentalPriceCalculatorUtils {

    /**
     * 根据租赁周期、时长和车辆租赁信息计算总费用。
     *
     * @param rentalPeriodEnum 租赁周期类型 (HOUR, DAY, MONTH)
     * @param rentalDuration   租赁时长 (例如: 5小时, 3天, 2个月)
     * @param vehicleRent      车辆租赁信息，包含各种价格
     * @return 租金总价
     */
    public static BigDecimal calculateTotalPrice(
            RentalPeriodEnum rentalPeriodEnum,
            long rentalDuration,
            VehicleRentDO vehicleRent) {

        if (rentalDuration <= 0 || vehicleRent == null) {
            throw new IllegalArgumentException("租赁时长必须大于0，车辆信息不能为空");
        }

        BigDecimal unitPrice;

        switch (rentalPeriodEnum) {
            case HOUR:
                // 按小时租赁，使用时租价格
                unitPrice = vehicleRent.getHourPrice();
                if (unitPrice == null || unitPrice.compareTo(BigDecimal.ZERO) <= 0) {
                    throw new IllegalArgumentException("时租价格必须大于0");
                }
                break;
            case DAY:
                // 按天租赁，使用日租价格
                unitPrice = vehicleRent.getDayPrice();
                if (unitPrice == null || unitPrice.compareTo(BigDecimal.ZERO) <= 0) {
                    throw new IllegalArgumentException("日租价格必须大于0");
                }
                break;
            case MONTH:
                // 按月租赁，使用月租价格
                unitPrice = vehicleRent.getMonthPrice();
                if (unitPrice == null || unitPrice.compareTo(BigDecimal.ZERO) <= 0) {
                    throw new IllegalArgumentException("月租价格必须大于0");
                }
                break;
            default:
                throw new IllegalArgumentException("不支持的租赁周期类型: " + rentalPeriodEnum);
        }

        return unitPrice.multiply(BigDecimal.valueOf(rentalDuration));
    }

    /**
     * 根据租赁周期、时长和每小时价格计算总费用。
     * @deprecated 请使用 {@link #calculateTotalPrice(RentalPeriodEnum, long, VehicleRentDO)} 方法
     *
     * @param rentalPeriodEnum 租赁周期类型 (HOUR, DAY, MONTH)
     * @param rentalDuration   租赁时长 (例如: 5小时, 3天, 2个月)
     * @param pricePerHour     每小时价格
     * @return 租金总价
     */
    @Deprecated
    public static BigDecimal calculateTotalPrice(
            RentalPeriodEnum rentalPeriodEnum,
            long rentalDuration,
            BigDecimal pricePerHour) {

        if (rentalDuration <= 0 || pricePerHour == null || pricePerHour.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("租赁时长和价格必须大于0");
        }

        long totalHours;

        switch (rentalPeriodEnum) {
            case HOUR:
                // 按小时租赁，时长就是小时数
                totalHours = rentalDuration;
                break;
            case DAY:
                // 按天租赁，时长转换成小时数
                totalHours = rentalDuration * 24;
                break;
            case MONTH:
                // 按月租赁，时长转换成小时数（简化处理，假设每月30天）
                totalHours = rentalDuration * 30 * 24;
                break;
            default:
                throw new IllegalArgumentException("不支持的租赁周期类型: " + rentalPeriodEnum);
        }

        return pricePerHour.multiply(BigDecimal.valueOf(totalHours));
    }

//    public static void main(String[] args) {
//        // 假设每小时价格是固定的 10.50 元
//        BigDecimal pricePerHour = new BigDecimal("10.50");
//
//        // 示例 1: 按小时租赁 5 小时
//        long durationHour = 5;
//        BigDecimal totalPriceHour = calculateTotalPrice(RentalPeriodEnum.HOUR, durationHour, pricePerHour);
//        System.out.println("按小时租赁:");
//        System.out.println("租赁时长: " + durationHour + " 小时");
//        System.out.println("总价: " + totalPriceHour); // 5 * 10.50 = 52.50
//        System.out.println("---");
//
//        // 示例 2: 按天租赁 3 天
//        long durationDay = 3;
//        BigDecimal totalPriceDay = calculateTotalPrice(RentalPeriodEnum.DAY, durationDay, pricePerHour);
//        System.out.println("按天租赁:");
//        System.out.println("租赁时长: " + durationDay + " 天");
//        System.out.println("总价: " + totalPriceDay); // 3 * 24 * 10.50 = 756.00
//        System.out.println("---");
//
//        // 示例 3: 按月租赁 2 个月
//        long durationMonth = 2;
//        BigDecimal totalPriceMonth = calculateTotalPrice(RentalPeriodEnum.MONTH, durationMonth, pricePerHour);
//        System.out.println("按月租赁:");
//        System.out.println("租赁时长: " + durationMonth + " 月");
//        System.out.println("总价: " + totalPriceMonth); // 2 * 30 * 24 * 10.50 = 15120.00
//        System.out.println("---");
//    }
}